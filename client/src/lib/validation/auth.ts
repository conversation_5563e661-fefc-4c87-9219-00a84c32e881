/**
 * Authentication form validation utilities
 */

import type {
  LoginFormData,
  RegisterFormData,
  PasswordChangeRequest,
  PasswordRequirements,
  ValidationErrors,
} from '@/types/auth'

// Common validation rules
export const EMAIL_REGEX = /^[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?\.)+[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?$/
export const STRONG_PASSWORD_REGEX = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+=[\]{};':"\\|,.<>/?])[A-Za-z\d!@#$%^&*()_+=[\]{};':"\\|,.<>/?]/
export const COMMON_PASSWORDS = [
  'password', '123456', '123456789', '12345678', '12345',
  'qwerty', 'abc123', 'password123', 'admin', 'letmein'
]

/**
 * Validate email format
 */
export function validateEmail(email: string): string | null {
  if (!email) {
    return 'Email is required'
  }
  if (!EMAIL_REGEX.test(email.toLowerCase())) {
    return 'Please enter a valid email address'
  }
  return null
}

/**
 * Validate password against requirements
 */
export function validatePassword(
  password: string, 
  requirements: PasswordRequirements = {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSymbols: true,
    forbidCommon: true,
  }
): { isValid: boolean; errors: string[] } {
  const errors: string[] = []

  if (!password) {
    return { isValid: false, errors: ['Password is required'] }
  }

  if (password.length < requirements.minLength) {
    errors.push(`Password must be at least ${requirements.minLength} characters long`)
  }

  if (requirements.requireUppercase && !/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter')
  }

  if (requirements.requireLowercase && !/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter')
  }

  if (requirements.requireNumbers && !/\d/.test(password)) {
    errors.push('Password must contain at least one number')
  }

  if (requirements.requireSymbols && !/[!@#$%^&*()_+=[\]{};':"\\|,.<>/?]/.test(password)) {
    errors.push('Password must contain at least one special character')
  }

  if (requirements.forbidCommon && COMMON_PASSWORDS.includes(password.toLowerCase())) {
    errors.push('Please choose a less common password')
  }

  return { isValid: errors.length === 0, errors }
}

/**
 * Validate passwords match
 */
export function validatePasswordsMatch(
  password: string, 
  confirmPassword: string
): string | null {
  if (password !== confirmPassword) {
    return 'Passwords do not match'
  }
  return null
}

/**
 * Validate professional license format
 */
export function validateProfessionalLicense(license: string): string | null {
  if (!license) {
    return null // Optional field
  }
  
  // Basic format validation - could be enhanced with state-specific rules
  if (license.length < 6) {
    return 'Professional license must be at least 6 characters'
  }
  
  // Remove spaces and check for alphanumeric
  const cleanLicense = license.replace(/\s/g, '')
  if (!/^[A-Z0-9]+$/i.test(cleanLicense)) {
    return 'Professional license should contain only letters and numbers'
  }
  
  return null
}

/**
 * Validate years of experience
 */
export function validateYearsExperience(years?: number): string | null {
  if (years === undefined || years === null) {
    return null // Optional field
  }
  
  if (years < 0 || years > 60) {
    return 'Years of experience must be between 0 and 60'
  }
  
  return null
}

/**
 * Validate login form data
 */
export function validateLoginForm(data: LoginFormData): ValidationErrors {
  const errors: ValidationErrors = {}

  // Username/email validation
  if (!data.username?.trim()) {
    errors.username = 'Email or username is required'
  } else if (data.username.includes('@')) {
    const emailError = validateEmail(data.username)
    if (emailError) {
      errors.username = emailError
    }
  }

  // Password validation
  if (!data.password) {
    errors.password = 'Password is required'
  }

  return errors
}


/**
 * Validate password change form data
 */
export function validatePasswordChangeForm(data: PasswordChangeRequest): ValidationErrors {
  const errors: ValidationErrors = {}

  // Current password validation
  if (!data.current_password) {
    errors.current_password = 'Current password is required'
  }

  // New password validation
  const passwordValidation = validatePassword(data.new_password)
  if (!passwordValidation.isValid) {
    errors.new_password = passwordValidation.errors
  }

  // Confirm password validation
  const passwordMatchError = validatePasswordsMatch(data.new_password, data.confirm_password)
  if (passwordMatchError) {
    errors.confirm_password = passwordMatchError
  }

  // Ensure new password is different from current
  if (data.current_password && data.new_password && data.current_password === data.new_password) {
    errors.new_password = 'New password must be different from current password'
  }

  return errors
}

/**
 * Check if validation errors object has any errors
 */
export function hasValidationErrors(errors: ValidationErrors): boolean {
  return Object.keys(errors).some(key => {
    const error = errors[key]
    if (Array.isArray(error)) {
      return error.length > 0
    }
    return error !== null && error !== undefined && error !== ''
  })
}

/**
 * Get first error message from validation errors
 */
export function getFirstErrorMessage(errors: ValidationErrors): string | null {
  for (const key of Object.keys(errors)) {
    const error = errors[key]
    if (Array.isArray(error) && error.length > 0) {
      return error[0]
    }
    if (typeof error === 'string' && error.trim()) {
      return error
    }
  }
  return null
}

/**
 * Password strength indicator
 */
export function getPasswordStrength(password: string): {
  score: number // 0-5
  label: string
  suggestions: string[]
} {
  if (!password) {
    return { score: 0, label: 'No password', suggestions: ['Enter a password'] }
  }

  const checks = {
    length: password.length >= 8,
    lowercase: /[a-z]/.test(password),
    uppercase: /[A-Z]/.test(password),
    numbers: /\d/.test(password),
    symbols: /[!@#$%^&*()_+=[\]{};':"\\|,.<>/?]/.test(password),
    notCommon: !COMMON_PASSWORDS.includes(password.toLowerCase()),
  }

  const score = Object.values(checks).filter(Boolean).length
  const suggestions: string[] = []

  if (!checks.length) suggestions.push('Use at least 8 characters')
  if (!checks.lowercase) suggestions.push('Add lowercase letters')
  if (!checks.uppercase) suggestions.push('Add uppercase letters')
  if (!checks.numbers) suggestions.push('Add numbers')
  if (!checks.symbols) suggestions.push('Add special characters')
  if (!checks.notCommon) suggestions.push('Avoid common passwords')

  const labels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong', 'Very Strong']

  return {
    score,
    label: labels[Math.min(score, labels.length - 1)] || 'Very Weak',
    suggestions: suggestions.slice(0, 3), // Show max 3 suggestions
  }
}

/**
 * Validate registration form data
 */
export function validateRegisterForm(data: RegisterFormData): ValidationErrors {
  const errors: ValidationErrors = {}

  // Full name validation
  if (!data.name?.trim()) {
    errors.name = 'Full name is required'
  } else if (data.name.trim().length < 2) {
    errors.name = 'Full name must be at least 2 characters long'
  }

  // Email validation (using username field as email)
  if (!data.username?.trim()) {
    errors.username = 'Email or username is required'
  } else {
    // Always validate as email if it looks like it should be an email
    const emailError = validateEmail(data.username)
    if (emailError) {
      errors.username = emailError
    }
  }

  // Password validation
  if (!data.password) {
    errors.password = 'Password is required'
  } else {
    const passwordValidation = validatePassword(data.password)
    if (!passwordValidation.isValid) {
      errors.password = passwordValidation.errors[0] || 'Password does not meet requirements'
    }
  }

  // Confirm password validation
  if (!data.confirm_password) {
    errors.confirm_password = 'Password confirmation is required'
  } else if (data.password && data.confirm_password !== data.password) {
    errors.confirm_password = 'Passwords do not match'
  }

  // Professional license validation (if provided)
  if (data.professional_license?.trim()) {
    const license = data.professional_license.trim()
    // Basic professional license format validation (PE-XXXXX or EIT-XXXXX)
    const licenseRegex = /^(PE|EIT|P\.E\.|E\.I\.T\.)-?[\w\d]{3,10}$/i
    if (!licenseRegex.test(license)) {
      errors.professional_license = 'Invalid license format. Expected format: PE-12345 or EIT-67890'
    }
  }

  // Years of experience validation (if provided)
  if (data.years_experience !== undefined) {
    if (data.years_experience < 0) {
      errors.years_experience = 'Years of experience must be a positive number'
    } else if (data.years_experience > 50) {
      errors.years_experience = 'Years of experience seems unusually high (max 50)'
    }
  }

  // Company name validation (if provided)
  if (data.company_name?.trim() && data.company_name.trim().length < 2) {
    errors.company_name = 'Company name must be at least 2 characters long'
  }

  // Job title validation (if provided)
  if (data.job_title?.trim() && data.job_title.trim().length < 2) {
    errors.job_title = 'Job title must be at least 2 characters long'
  }

  // Specializations validation (if provided)
  if (data.specializations?.trim()) {
    const specs = data.specializations.split(',').map(s => s.trim()).filter(Boolean)
    if (specs.some(spec => spec.length < 3)) {
      errors.specializations = 'Each specialization must be at least 3 characters long'
    }
    if (specs.length > 10) {
      errors.specializations = 'Please limit to 10 specializations maximum'
    }
  }

  return errors
}