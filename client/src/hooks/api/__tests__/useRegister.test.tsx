/**
 * @file useRegister hook tests
 * @description TDD tests for user registration service
 */

import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { renderHook, act, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'

import { useRegister } from '../useAuth'
import type { RegisterRequest, RegisterResponse } from '@/types/auth'

// Mock functions with proper hoisting
const { mockRegister, mockInvalidateQueries, mockSetAuth } = vi.hoisted(() => ({
  mockRegister: vi.fn(),
  mockInvalidateQueries: vi.fn(),
  mockSetAuth: vi.fn(),
}))

vi.mock('@/lib/api', () => ({
  apiClient: {
    register: mockRegister,
  },
  MutationKeys: {
    auth: {
      register: ['auth', 'register'],
    },
  },
  QueryKeys: {
    users: {
      all: ['users', 'all'],
    },
  },
}))

// Mock auth store
vi.mock('@/stores/authStore', () => ({
  useAuthStore: () => ({
    setAuth: mockSetAuth,
  }),
}))

// Test wrapper
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  queryClient.invalidateQueries = mockInvalidateQueries

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('useRegister', () => {
  const mockRegistrationData: RegisterRequest = {
    name: 'John Doe',
    email: '<EMAIL>',
    password: 'SecurePass123!',
    confirm_password: 'SecurePass123!',
    professional_license: 'EIT-12345',
    company_name: 'Electrical Solutions Inc',
    job_title: 'Senior Electrical Engineer',
    years_experience: 8,
    specializations: ['Power Systems', 'Industrial Control'],
  }

  const mockRegistrationResponse: RegisterResponse = {
    message: 'Registration successful',
    user: {
      id: 1,
      name: 'John Doe',
      email: '<EMAIL>',
      is_superuser: false,
      is_active: true,
      role: 'electrical_engineer',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
    },
    requires_verification: false,
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  // Success Cases
  describe('Successful Registration', () => {
    it('should successfully register a user with professional credentials', async () => {
      mockRegister.mockResolvedValueOnce(mockRegistrationResponse)

      const wrapper = createWrapper()
      const { result } = renderHook(() => useRegister(), { wrapper })

      act(() => {
        result.current.mutate(mockRegistrationData)
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(mockRegister).toHaveBeenCalledWith(mockRegistrationData)
      expect(mockInvalidateQueries).toHaveBeenCalledWith({ 
        queryKey: ['users', 'all'] 
      })
    })

    it('should register a user with minimal required data', async () => {
      const minimalData: RegisterRequest = {
        name: 'Jane Smith',
        email: '<EMAIL>',
        password: 'SecurePass123!',
        confirm_password: 'SecurePass123!',
      }

      const minimalResponse: RegisterResponse = {
        message: 'Registration successful',
        user: {
          id: 2,
          name: 'Jane Smith',
          email: '<EMAIL>',
          is_superuser: false,
          is_active: true,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
        requires_verification: true,
      }

      mockRegister.mockResolvedValueOnce(minimalResponse)

      const wrapper = createWrapper()
      const { result } = renderHook(() => useRegister(), { wrapper })

      act(() => {
        result.current.mutate(minimalData)
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(mockRegister).toHaveBeenCalledWith(minimalData)
      expect(result.current.data).toEqual(minimalResponse)
    })

    it('should handle registration requiring email verification', async () => {
      const verificationResponse: RegisterResponse = {
        ...mockRegistrationResponse,
        requires_verification: true,
        message: 'Registration successful. Please check your email to verify your account.',
      }

      mockRegister.mockResolvedValueOnce(verificationResponse)

      const wrapper = createWrapper()
      const { result } = renderHook(() => useRegister(), { wrapper })

      act(() => {
        result.current.mutate(mockRegistrationData)
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data?.requires_verification).toBe(true)
      expect(result.current.data?.message).toContain('verify your account')
    })
  })

  // Error Cases
  describe('Registration Errors', () => {
    it('should handle email already exists error', async () => {
      const emailExistsError = {
        message: 'User with this email already exists',
        status: 400,
      }

      mockRegister.mockRejectedValueOnce(emailExistsError)

      const wrapper = createWrapper()
      const { result } = renderHook(() => useRegister(), { wrapper })

      act(() => {
        result.current.mutate(mockRegistrationData)
      })

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error).toEqual(emailExistsError)
    })

    it('should handle validation errors', async () => {
      const validationError = {
        message: 'Validation failed',
        errors: {
          password: 'Password must be at least 8 characters long',
          email: 'Please enter a valid email address',
        },
        status: 422,
      }

      mockRegister.mockRejectedValueOnce(validationError)

      const wrapper = createWrapper()
      const { result } = renderHook(() => useRegister(), { wrapper })

      const invalidData: RegisterRequest = {
        name: 'Test User',
        email: 'invalid-email',
        password: '123',
        confirm_password: '123',
      }

      act(() => {
        result.current.mutate(invalidData)
      })

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error).toEqual(validationError)
    })

    it('should handle network errors', async () => {
      const networkError = {
        message: 'Network error occurred',
        status: 500,
      }

      mockRegister.mockRejectedValueOnce(networkError)

      const wrapper = createWrapper()
      const { result } = renderHook(() => useRegister(), { wrapper })

      act(() => {
        result.current.mutate(mockRegistrationData)
      })

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error).toEqual(networkError)
    })

    it('should handle password mismatch error', async () => {
      const passwordMismatchError = {
        message: 'Password confirmation does not match',
        status: 400,
      }

      mockRegister.mockRejectedValueOnce(passwordMismatchError)

      const wrapper = createWrapper()
      const { result } = renderHook(() => useRegister(), { wrapper })

      const mismatchData: RegisterRequest = {
        ...mockRegistrationData,
        confirm_password: 'DifferentPassword123!',
      }

      act(() => {
        result.current.mutate(mismatchData)
      })

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error).toEqual(passwordMismatchError)
    })
  })

  // Loading States
  describe('Loading States', () => {
    it('should show loading state during registration', async () => {
      // Create a long-running promise to test loading state
      const longRunningPromise = new Promise<RegisterResponse>((resolve) => {
        setTimeout(() => resolve(mockRegistrationResponse), 100)
      })

      mockRegister.mockReturnValueOnce(longRunningPromise)

      const wrapper = createWrapper()
      const { result } = renderHook(() => useRegister(), { wrapper })

      // Start mutation
      act(() => {
        result.current.mutate(mockRegistrationData)
      })

      // Should be loading immediately after calling mutate
      await waitFor(() => {
        expect(result.current.isPending).toBe(true)
      })

      expect(result.current.isSuccess).toBe(false)
      expect(result.current.isError).toBe(false)

      // Wait for promise to resolve
      await waitFor(() => {
        expect(result.current.isPending).toBe(false)
        expect(result.current.isSuccess).toBe(true)
      })
    })
  })

  // Professional Credentials Validation
  describe('Professional Credentials', () => {
    it('should handle registration with professional license validation', async () => {
      const professionalData: RegisterRequest = {
        name: 'Professional Engineer',
        email: '<EMAIL>',
        password: 'SecurePass123!',
        confirm_password: 'SecurePass123!',
        professional_license: 'PE-54321',
        company_name: 'Engineering Firm LLC',
        job_title: 'Principal Electrical Engineer',
        years_experience: 15,
        specializations: ['Power Generation', 'Transmission Systems', 'Renewable Energy'],
      }

      const professionalResponse: RegisterResponse = {
        message: 'Professional registration successful',
        user: {
          id: 3,
          name: 'Professional Engineer',
          email: '<EMAIL>',
          is_superuser: false,
          is_active: true,
          role: 'principal_engineer',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
        requires_verification: false,
      }

      mockRegister.mockResolvedValueOnce(professionalResponse)

      const wrapper = createWrapper()
      const { result } = renderHook(() => useRegister(), { wrapper })

      act(() => {
        result.current.mutate(professionalData)
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(mockRegister).toHaveBeenCalledWith(professionalData)
      expect(result.current.data?.user.role).toBe('principal_engineer')
    })

    it('should handle invalid professional license error', async () => {
      const invalidLicenseError = {
        message: 'Invalid professional license number',
        errors: {
          professional_license: 'License number format is invalid',
        },
        status: 422,
      }

      mockRegister.mockRejectedValueOnce(invalidLicenseError)

      const wrapper = createWrapper()
      const { result } = renderHook(() => useRegister(), { wrapper })

      const invalidLicenseData: RegisterRequest = {
        ...mockRegistrationData,
        professional_license: 'INVALID-LICENSE',
      }

      act(() => {
        result.current.mutate(invalidLicenseData)
      })

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error).toEqual(invalidLicenseError)
    })
  })

  // Integration with Query Client
  describe('Query Client Integration', () => {
    it('should invalidate users queries after successful registration', async () => {
      mockRegister.mockResolvedValueOnce(mockRegistrationResponse)

      const wrapper = createWrapper()
      const { result } = renderHook(() => useRegister(), { wrapper })

      act(() => {
        result.current.mutate(mockRegistrationData)
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(mockInvalidateQueries).toHaveBeenCalledWith({
        queryKey: ['users', 'all'],
      })
    })

    it('should not invalidate queries on registration failure', async () => {
      const registrationError = {
        message: 'Registration failed',
        status: 400,
      }

      mockRegister.mockRejectedValueOnce(registrationError)

      const wrapper = createWrapper()
      const { result } = renderHook(() => useRegister(), { wrapper })

      act(() => {
        result.current.mutate(mockRegistrationData)
      })

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(mockInvalidateQueries).not.toHaveBeenCalled()
    })
  })
})