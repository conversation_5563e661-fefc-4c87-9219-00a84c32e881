/**
 * @file AuthLayout template component
 * @description Centralized layout for authentication pages with branding and responsive design
 */

import * as React from 'react'
import { cn } from '@/lib/utils'
import { UnifiedIcon as Icon } from '@/components/atoms/icon'

export interface AuthLayoutProps {
  /** Child components to render in the auth container */
  children: React.ReactNode
  /** Optional title for the auth section */
  title?: string
  /** Optional description for the auth section */
  description?: string
  /** Whether to show the brand logo */
  showBrandLogo?: boolean
  /** Additional CSS classes */
  className?: string
  /** Test identifier */
  'data-testid'?: string
}

export const AuthLayout = React.forwardRef<HTMLElement, AuthLayoutProps>(
  (
    {
      children,
      title,
      description,
      showBrandLogo = true,
      className,
      'data-testid': dataTestId,
    },
    ref
  ) => {
    return (
      <main
        ref={ref}
        className="min-h-screen flex items-center justify-center bg-background text-foreground px-4 py-12 sm:px-6 lg:px-8"
        data-testid={dataTestId}
      >
        <div
          data-testid="auth-container"
          className={cn('w-full max-w-md space-y-8', className)}
        >
          {/* Brand Logo Section */}
          {showBrandLogo && (
            <div data-testid="brand-logo" className="flex justify-center items-center space-x-2">
              <Icon
                type="zap"
                data-testid="electrical-icon"
                className="h-8 w-8 text-primary"
                aria-hidden="true"
              />
              <span className="text-2xl font-bold text-primary">
                Ultimate Electrical Designer
              </span>
            </div>
          )}

          {/* Header Section */}
          {(title || description) && (
            <div className="text-center space-y-2">
              {title && (
                <h1 className="text-3xl font-extrabold text-foreground">
                  {title}
                </h1>
              )}
              {description && (
                <p className="text-sm text-muted-foreground">
                  {description}
                </p>
              )}
            </div>
          )}

          {/* Content Section */}
          {children}
        </div>
      </main>
    )
  }
)

AuthLayout.displayName = 'AuthLayout'