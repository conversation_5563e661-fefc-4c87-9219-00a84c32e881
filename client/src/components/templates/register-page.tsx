/**
 * @file RegisterPage template component
 * @description Complete registration page with AuthLayout integration and professional credentials support
 */

import * as React from 'react'
import { cn } from '@/lib/utils'
import { AuthLayout } from './auth-layout'
import { RegistrationForm } from '@/components/organisms/registration-form'

export interface RegisterPageProps {
  /** Custom title for the registration page */
  title?: string
  /** Custom description for the registration page */
  description?: string
  /** Whether to show brand logo */
  showBrandLogo?: boolean
  /** Whether to show professional credentials fields */
  showProfessionalFields?: boolean
  /** Whether to show sign in link */
  showSignInLink?: boolean
  /** Callback fired on successful registration */
  onSuccess?: () => void
  /** Callback fired on registration error */
  onError?: (error: any) => void
  /** Callback fired when sign in link is clicked */
  onSignIn?: () => void
  /** Additional CSS classes */
  className?: string
  /** Test identifier */
  'data-testid'?: string
}

export const RegisterPage = React.forwardRef<HTMLElement, RegisterPageProps>(
  (
    {
      title = 'Create Your Account',
      description = 'Join the Ultimate Electrical Designer platform and start designing professional electrical systems',
      showBrandLogo = true,
      showProfessionalFields = false,
      showSignInLink = true,
      onSuccess,
      onError,
      onSignIn,
      className,
      'data-testid': dataTestId,
    },
    ref
  ) => {
    return (
      <AuthLayout
        ref={ref}
        title={title}
        description={description}
        showBrandLogo={showBrandLogo}
        className={className}
        data-testid={dataTestId}
      >
        <RegistrationForm
          title=""
          description=""
          showProfessionalFields={showProfessionalFields}
          showSignInLink={showSignInLink}
          onSuccess={onSuccess}
          onError={onError}
          onSignIn={onSignIn}
          className="w-full"
        />
      </AuthLayout>
    )
  }
)

RegisterPage.displayName = 'RegisterPage'