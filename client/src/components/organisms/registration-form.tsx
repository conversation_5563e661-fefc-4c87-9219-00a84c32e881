/**
 * @file RegistrationForm organism component
 * @description Complete registration form with professional credentials validation and auth integration
 */

import * as React from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/atoms/button'
import { UnifiedIcon as Icon } from '@/components/atoms/icon'
import { InputField } from '@/components/molecules/input-field'
import { PasswordInput } from '@/components/molecules/password-input'
import { useRegister } from '@/hooks/api/useAuth'
import { useAuthStore } from '@/stores/authStore'
import { validateRegisterForm } from '@/lib/validation/auth'
import type { ValidationErrors, RegisterRequest, RegisterFormData } from '@/types/auth'

export interface RegistrationFormProps {
  /** Custom title for the form */
  title?: string
  /** Custom description for the form */
  description?: string
  /** Show professional credentials fields */
  showProfessionalFields?: boolean
  /** Show sign in link */
  showSignInLink?: boolean
  /** Callback fired on successful registration */
  onSuccess?: () => void
  /** Callback fired on registration error */
  onError?: (error: any) => void
  /** Callback fired when sign in link is clicked */
  onSignIn?: () => void
  /** Additional CSS classes */
  className?: string
  /** Test identifier */
  'data-testid'?: string
}

export const RegistrationForm = React.forwardRef<HTMLFormElement, RegistrationFormProps>(
  (
    {
      title = 'Create Your Account',
      description = 'Join the Ultimate Electrical Designer platform',
      showProfessionalFields = false,
      showSignInLink = true,
      onSuccess,
      onError,
      onSignIn,
      className,
      'data-testid': dataTestId,
    },
    ref
  ) => {
    // Auth hooks and state
    const register = useRegister()
    const { isLoading } = useAuthStore()

    // Form state
    const [formData, setFormData] = React.useState<RegisterFormData>({
      name: '',
      username: '',
      password: '',
      confirm_password: '',
      professional_license: '',
      company_name: '',
      job_title: '',
      years_experience: undefined,
      specializations: '',
    })

    // Validation state
    const [errors, setErrors] = React.useState<ValidationErrors>({})
    const [isValidating, setIsValidating] = React.useState(false)

    // Handle input changes
    const handleInputChange = React.useCallback(
      (field: keyof RegisterFormData) => (event: React.ChangeEvent<HTMLInputElement>) => {
        const value = event.target.value
        
        setFormData(prev => ({
          ...prev,
          [field]: field === 'years_experience' ? (value ? Number(value) : undefined) : value,
        }))

        // Clear field error when user starts typing
        if (errors[field]) {
          setErrors(prev => {
            const newErrors = { ...prev }
            delete newErrors[field]
            return newErrors
          })
        }
      },
      [errors]
    )

    // Validate form
    const validateForm = React.useCallback((): boolean => {
      setIsValidating(true)
      
      try {
        const validationErrors = validateRegisterForm(formData)
        setErrors(validationErrors)
        return Object.keys(validationErrors).length === 0
      } catch (error) {
        // Handle unexpected errors
        setErrors({ form: 'Validation failed' })
        return false
      } finally {
        setIsValidating(false)
      }
    }, [formData])

    // Handle form submission
    const handleSubmit = React.useCallback(
      async (event: React.FormEvent<HTMLFormElement>) => {
        event.preventDefault()
        
        // Validate form
        if (!validateForm()) {
          return
        }

        // Convert form data to RegisterRequest format
        const registerData: RegisterRequest = {
          name: formData.name,
          email: formData.username,
          password: formData.password,
          confirm_password: formData.confirm_password,
        }

        // Add professional fields if provided
        if (showProfessionalFields) {
          if (formData.professional_license?.trim()) {
            registerData.professional_license = formData.professional_license.trim()
          }
          if (formData.company_name?.trim()) {
            registerData.company_name = formData.company_name.trim()
          }
          if (formData.job_title?.trim()) {
            registerData.job_title = formData.job_title.trim()
          }
          if (formData.years_experience) {
            registerData.years_experience = formData.years_experience
          }
          if (formData.specializations?.trim()) {
            registerData.specializations = formData.specializations.split(',').map(s => s.trim()).filter(Boolean)
          }
        }

        // Submit registration
        register.mutate(registerData)
      },
      [formData, register, validateForm, showProfessionalFields]
    )

    // Handle registration success
    React.useEffect(() => {
      if (register.isSuccess) {
        onSuccess?.()
      }
    }, [register.isSuccess, onSuccess])

    // Handle registration error
    React.useEffect(() => {
      if (register.error) {
        onError?.(register.error)
      }
    }, [register.error, onError])

    // Determine loading state
    const isSubmitting = register.isPending || isLoading
    const hasServerError = Boolean(register.error)

    return (
      <form
        ref={ref}
        onSubmit={handleSubmit}
        className={cn(
          'w-full max-w-lg space-y-6 rounded-lg border bg-card p-6 shadow-sm',
          className
        )}
        data-testid={dataTestId}
        noValidate
      >
        {/* Header */}
        <div className="space-y-2 text-center">
          <h1 className="text-2xl font-semibold tracking-tight text-foreground">
            {title}
          </h1>
          {description && (
            <p className="text-sm text-muted-foreground">
              {description}
            </p>
          )}
        </div>

        {/* Server Error Display */}
        {hasServerError && (
          <div
            role="alert"
            className="rounded-md border border-destructive bg-destructive/10 p-3"
          >
            <div className="flex items-center space-x-2">
              <Icon type="alert" className="h-4 w-4 text-destructive" />
              <p className="text-sm text-destructive">
                {register.error?.message || 'An error occurred during registration'}
              </p>
            </div>
          </div>
        )}

        {/* Personal Information Section */}
        <div className="space-y-4">
          <h2 className="text-lg font-medium text-foreground">Personal Information</h2>
          
          {/* Full Name Field */}
          <InputField
            label="Full Name"
            type="text"
            value={formData.name}
            onChange={handleInputChange('name')}
            error={errors.name}
            required
            disabled={isSubmitting}
            placeholder="Enter your full name"
            autoComplete="name"
            className="w-full"
          />

          {/* Email Field */}
          <InputField
            label="Email Address"
            type="email"
            value={formData.username}
            onChange={handleInputChange('username')}
            error={errors.username}
            required
            disabled={isSubmitting}
            placeholder="Enter your email address"
            autoComplete="email"
            className="w-full"
          />

          {/* Password Field */}
          <PasswordInput
            label="Password"
            value={formData.password}
            onChange={handleInputChange('password')}
            error={errors.password}
            required
            disabled={isSubmitting}
            placeholder="Enter your password"
            autoComplete="new-password"
            className="w-full"
          />

          {/* Confirm Password Field */}
          <PasswordInput
            label="Confirm Password"
            value={formData.confirm_password}
            onChange={handleInputChange('confirm_password')}
            error={errors.confirm_password}
            required
            disabled={isSubmitting}
            placeholder="Confirm your password"
            autoComplete="new-password"
            className="w-full"
          />
        </div>

        {/* Professional Credentials Section */}
        {showProfessionalFields && (
          <div className="space-y-4">
            <div className="border-t pt-4">
              <h2 className="text-lg font-medium text-foreground">Professional Credentials</h2>
              <p className="text-sm text-muted-foreground">
                Optional professional information for electrical design verification
              </p>
            </div>

            {/* Professional License */}
            <InputField
              label="Professional License"
              type="text"
              value={formData.professional_license || ''}
              onChange={handleInputChange('professional_license')}
              error={errors.professional_license}
              disabled={isSubmitting}
              placeholder="e.g., PE-12345, EIT-67890"
              helpText="Professional Engineer (PE) or Engineer in Training (EIT) license number"
              className="w-full"
            />

            {/* Company Name */}
            <InputField
              label="Company Name"
              type="text"
              value={formData.company_name || ''}
              onChange={handleInputChange('company_name')}
              error={errors.company_name}
              disabled={isSubmitting}
              placeholder="Enter your company or organization"
              autoComplete="organization"
              className="w-full"
            />

            {/* Job Title */}
            <InputField
              label="Job Title"
              type="text"
              value={formData.job_title || ''}
              onChange={handleInputChange('job_title')}
              error={errors.job_title}
              disabled={isSubmitting}
              placeholder="e.g., Electrical Engineer, Senior Designer"
              autoComplete="organization-title"
              className="w-full"
            />

            {/* Years of Experience */}
            <InputField
              label="Years of Experience"
              type="number"
              value={formData.years_experience?.toString() || ''}
              onChange={handleInputChange('years_experience')}
              error={errors.years_experience}
              disabled={isSubmitting}
              placeholder="0"
              min="0"
              max="50"
              className="w-full"
            />

            {/* Specializations */}
            <InputField
              label="Specializations"
              type="text"
              value={formData.specializations || ''}
              onChange={handleInputChange('specializations')}
              error={errors.specializations}
              disabled={isSubmitting}
              placeholder="e.g., Power Systems, Industrial Control, Renewable Energy"
              helpText="Comma-separated list of your electrical engineering specializations"
              className="w-full"
            />
          </div>
        )}

        {/* Submit Button */}
        <Button
          type="submit"
          className="w-full"
          disabled={isSubmitting || isValidating}
          aria-disabled={isSubmitting || isValidating}
        >
          {isSubmitting ? (
            <>
              <Icon type="refresh" className="mr-2 h-4 w-4 animate-spin" />
              Creating Account...
            </>
          ) : (
            <>
              <Icon type="user" className="mr-2 h-4 w-4" />
              Create Account
            </>
          )}
        </Button>

        {/* Sign In Link */}
        {showSignInLink && (
          <div className="text-center text-sm text-muted-foreground">
            Already have an account?{' '}
            <button
              type="button"
              onClick={onSignIn}
              className="font-medium text-primary hover:text-primary/80 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
              disabled={isSubmitting}
            >
              Sign in
            </button>
          </div>
        )}
      </form>
    )
  }
)

RegistrationForm.displayName = 'RegistrationForm'